import 'dart:io';

import 'package:drift/drift.dart';
import 'package:drift/native.dart';
import 'package:path/path.dart' as p;
import 'package:path_provider/path_provider.dart';
import 'package:sqlite3/sqlite3.dart';
import 'package:sqlite3_flutter_libs/sqlite3_flutter_libs.dart';

import '../models/app_settings_table.dart';
import '../models/income_table.dart';
import '../models/level_settings_table.dart';
import '../models/orders_table.dart';
import '../models/performance_table.dart';
import '../models/spare_parts_history_table.dart';
import '../models/spare_parts_table.dart';

part 'database.g.dart';

/// Main database class for the bitrackr application.
/// Uses Drift ORM for SQLite database operations.
@DriftDatabase(
  tables: [
    IncomeTable,
    OrdersTable,
    PerformanceTable,
    SparePartsTable,
    SparePartsHistoryTable,
    LevelSettingsTable,
    AppSettingsTable,
  ],
)
class AppDatabase extends _$AppDatabase {
  AppDatabase() : super(_openConnection());

  @override
  int get schemaVersion => 1;

  @override
  MigrationStrategy get migration => MigrationStrategy(
    onCreate: (Migrator m) async {
      await m.createAll();
      await _insertDefaultData();
    },
    onUpgrade: (Migrator m, int from, int to) async {
      // Handle database migrations here when schema version changes
    },
  );

  /// Insert default data into the database
  Future<void> _insertDefaultData() async {
    // Insert default level settings
    await into(levelSettingsTable).insert(
      LevelSettingsTableCompanion.insert(
        platinumPointsReq: 500,
        platinumBidReq: 0.95,
        platinumTripReq: 0.98,
        goldPointsReq: 350,
        goldBidReq: 0.90,
        goldTripReq: 0.95,
        silverPointsReq: 200,
        silverBidReq: 0.85,
        silverTripReq: 0.90,
      ),
    );

    // Insert default app settings
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month);
    final endOfMonth = DateTime(now.year, now.month + 1, 0);

    await into(appSettingsTable).insert(
      AppSettingsTableCompanion.insert(
        dateRangeStart: startOfMonth,
        dateRangeEnd: endOfMonth,
        backupDirectoryPath: const Value(null),
        updatedAt: Value(now),
        lastSyncTime: const Value(null),
      ),
    );
  }
}

/// Open database connection with proper configuration
LazyDatabase _openConnection() => LazyDatabase(() async {
  // Ensure sqlite3 is properly initialized on mobile platforms
  if (Platform.isAndroid) {
    await applyWorkaroundToOpenSqlite3OnOldAndroidVersions();
  }

  // Get the application documents directory
  final dbFolder = await getApplicationDocumentsDirectory();
  final file = File(p.join(dbFolder.path, 'bitrackr.db'));

  // Configure sqlite3 to use the bundled library on Linux
  if (Platform.isLinux) {
    sqlite3.tempDirectory = (await getTemporaryDirectory()).path;
  }

  return NativeDatabase.createInBackground(file);
});
