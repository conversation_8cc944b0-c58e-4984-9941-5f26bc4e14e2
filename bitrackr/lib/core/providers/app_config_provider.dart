import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../config/app_config.dart';

/// Provider for the application configuration instance.
/// This provider manages the app configuration and environment variables.
final appConfigProvider = Provider<AppConfig>((ref) => AppConfig.instance);

/// Provider for app configuration initialization status
final appConfigInitializedProvider = FutureProvider<bool>((ref) async {
  final config = ref.watch(appConfigProvider);

  try {
    await config.initialize();
    return true;
  } on Exception {
    return false;
  }
});

/// Provider for Supabase configuration status
final supabaseConfigStatusProvider = Provider<bool>((ref) {
  final config = ref.watch(appConfigProvider);
  return config.isSupabaseConfigured;
});

/// Provider for app configuration summary
final appConfigSummaryProvider = Provider<Map<String, dynamic>>((ref) {
  final config = ref.watch(appConfigProvider);
  return config.configSummary;
});

/// Provider for debug mode status
final debugModeProvider = Provider<bool>((ref) {
  final config = ref.watch(appConfigProvider);
  return config.debugMode;
});

/// Provider for logging enabled status
final loggingEnabledProvider = Provider<bool>((ref) {
  final config = ref.watch(appConfigProvider);
  return config.enableLogging;
});

/// Provider for app name
final appNameProvider = Provider<String>((ref) {
  final config = ref.watch(appConfigProvider);
  return config.appName;
});

/// Provider for app version
final appVersionProvider = Provider<String>((ref) {
  final config = ref.watch(appConfigProvider);
  return config.appVersion;
});

/// Provider for database name
final databaseNameProvider = Provider<String>((ref) {
  final config = ref.watch(appConfigProvider);
  return config.databaseName;
});
