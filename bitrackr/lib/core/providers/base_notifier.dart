import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../errors/error_handler.dart';
import '../errors/failures.dart';
import 'base_state.dart';

/// Base notifier class for handling common state management patterns.
/// Provides utility methods for error handling and state transitions.
abstract class BaseNotifier<T> extends StateNotifier<BaseState<T>> {
  BaseNotifier() : super(const BaseState.initial());

  /// Execute an async operation with automatic error handling
  Future<void> executeAsync(Future<T> Function() operation) async {
    try {
      state = const BaseState.loading();
      final result = await operation();
      state = BaseState.success(result);
    } on Exception catch (e, stackTrace) {
      final failure = ErrorHandler.handleException(e, stackTrace);
      state = BaseState.error(failure);
    }
  }

  /// Execute an async operation without changing to loading state
  Future<void> executeAsyncSilent(Future<T> Function() operation) async {
    try {
      final result = await operation();
      state = BaseState.success(result);
    } on Exception catch (e, stackTrace) {
      final failure = ErrorHandler.handleException(e, stackTrace);
      state = BaseState.error(failure);
    }
  }

  /// Execute a sync operation with automatic error handling
  void executeSync(T Function() operation) {
    try {
      state = const BaseState.loading();
      final result = operation();
      state = BaseState.success(result);
    } on Exception catch (e, stackTrace) {
      final failure = ErrorHandler.handleException(e, stackTrace);
      state = BaseState.error(failure);
    }
  }

  /// Set loading state
  void setLoading() {
    state = const BaseState.loading();
  }

  /// Set success state
  void setSuccess(T data) {
    state = BaseState.success(data);
  }

  /// Set error state
  void setError(Failure failure) {
    state = BaseState.error(failure);
  }

  /// Reset to initial state
  void reset() {
    state = const BaseState.initial();
  }

  /// Update data if currently in success state
  void updateData(T Function(T current) updater) {
    state.whenOrNull(
      success: (data) => state = BaseState.success(updater(data)),
    );
  }

  /// Transform current data if in success state
  void transformData<R>(R Function(T data) transformer) {
    state.whenOrNull(
      success: (data) {
        try {
          final transformed = transformer(data);
          if (transformed is T) {
            state = BaseState.success(transformed);
          }
        } on Exception catch (e, stackTrace) {
          final failure = ErrorHandler.handleException(e, stackTrace);
          state = BaseState.error(failure);
        }
      },
    );
  }
}

/// Base paginated notifier for handling lists with pagination
abstract class BasePaginatedNotifier<T>
    extends StateNotifier<PaginatedState<T>> {
  BasePaginatedNotifier() : super(const PaginatedState.initial());

  /// Load initial data
  Future<void> loadInitial(Future<List<T>> Function() operation) async {
    try {
      state = const PaginatedState.loading();
      final result = await operation();
      state = PaginatedState.success(
        data: result,
        hasMore: result.isNotEmpty,
        currentPage: 1,
      );
    } on Exception catch (e, stackTrace) {
      final failure = ErrorHandler.handleException(e, stackTrace);
      state = PaginatedState.error(failure);
    }
  }

  /// Load more data
  Future<void> loadMore(Future<List<T>> Function(int page) operation) async {
    final currentState = state;
    if (currentState is! PaginatedSuccess<T> || !currentState.hasMore) {
      return;
    }

    try {
      state = PaginatedState.loadingMore(currentState.data);
      final nextPage = currentState.currentPage + 1;
      final newData = await operation(nextPage);

      final allData = [...currentState.data, ...newData];
      state = PaginatedState.success(
        data: allData,
        hasMore: newData.isNotEmpty,
        currentPage: nextPage,
      );
    } on Exception catch (e, stackTrace) {
      final failure = ErrorHandler.handleException(e, stackTrace);
      state = PaginatedState.error(failure);
    }
  }

  /// Refresh data
  Future<void> refresh(Future<List<T>> Function() operation) async {
    await loadInitial(operation);
  }

  /// Add item to the list
  void addItem(T item) {
    state.whenOrNull(
      success: (data, hasMore, currentPage) {
        state = PaginatedState.success(
          data: [item, ...data],
          hasMore: hasMore,
          currentPage: currentPage,
        );
      },
    );
  }

  /// Remove item from the list
  void removeItem(bool Function(T item) predicate) {
    state.whenOrNull(
      success: (data, hasMore, currentPage) {
        final updatedData = data.where((item) => !predicate(item)).toList();
        state = PaginatedState.success(
          data: updatedData,
          hasMore: hasMore,
          currentPage: currentPage,
        );
      },
    );
  }

  /// Update item in the list
  void updateItem(bool Function(T item) predicate, T Function(T item) updater) {
    state.whenOrNull(
      success: (data, hasMore, currentPage) {
        final updatedData = data
            .map((item) => predicate(item) ? updater(item) : item)
            .toList();
        state = PaginatedState.success(
          data: updatedData,
          hasMore: hasMore,
          currentPage: currentPage,
        );
      },
    );
  }

  /// Reset to initial state
  void reset() {
    state = const PaginatedState.initial();
  }
}
