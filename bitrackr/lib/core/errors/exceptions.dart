/// Base exception class for the bitrackr application.
/// All custom exceptions should extend this class.
abstract class AppException implements Exception {
  const AppException(this.message);

  final String message;

  @override
  String toString() => 'AppException: $message';
}

/// Database-related exceptions
class DatabaseException extends AppException {
  const DatabaseException(super.message);

  @override
  String toString() => 'DatabaseException: $message';
}

/// Network-related exceptions
class NetworkException extends AppException {
  const NetworkException(super.message);

  @override
  String toString() => 'NetworkException: $message';
}

/// Validation-related exceptions
class ValidationException extends AppException {
  const ValidationException(super.message);

  @override
  String toString() => 'ValidationException: $message';
}

/// Not found exceptions
class NotFoundException extends AppException {
  const NotFoundException(super.message);

  @override
  String toString() => 'NotFoundException: $message';
}

/// Business logic exceptions
class BusinessLogicException extends AppException {
  const BusinessLogicException(super.message);

  @override
  String toString() => 'BusinessLogicException: $message';
}

/// Authentication exceptions
class AuthenticationException extends AppException {
  const AuthenticationException(super.message);

  @override
  String toString() => 'AuthenticationException: $message';
}

/// Authorization exceptions
class AuthorizationException extends AppException {
  const AuthorizationException(super.message);

  @override
  String toString() => 'AuthorizationException: $message';
}

/// Sync-related exceptions
class SyncException extends AppException {
  const SyncException(super.message);

  @override
  String toString() => 'SyncException: $message';
}

/// Storage-related exceptions
class AppStorageException extends AppException {
  const AppStorageException(super.message);

  @override
  String toString() => 'AppStorageException: $message';
}

/// Parsing-related exceptions
class ParsingException extends AppException {
  const ParsingException(super.message);

  @override
  String toString() => 'ParsingException: $message';
}

/// Timeout-related exceptions
class TimeoutException extends AppException {
  const TimeoutException(super.message);

  @override
  String toString() => 'TimeoutException: $message';
}

/// Exception wrapper for Failure objects to comply with only_throw_errors lint rule
class FailureException extends AppException {
  const FailureException(super.message, this.failure);

  final Object failure;

  @override
  String toString() => 'FailureException: $message (${failure.runtimeType})';
}

/// Cache-related exceptions
class CacheException extends AppException {
  const CacheException(super.message);

  @override
  String toString() => 'CacheException: $message';
}

/// Configuration exceptions
class ConfigurationException extends AppException {
  const ConfigurationException(super.message);

  @override
  String toString() => 'ConfigurationException: $message';
}

/// File operation exceptions
class FileException extends AppException {
  const FileException(super.message);

  @override
  String toString() => 'FileException: $message';
}
