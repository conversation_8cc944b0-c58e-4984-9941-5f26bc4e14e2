import 'dart:io';

import 'package:drift/drift.dart';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import 'exceptions.dart';
import 'failures.dart';

/// Global error handler for converting exceptions to failures.
/// Provides centralized error handling and logging throughout the application.
class ErrorHandler {
  ErrorHandler._();

  /// Convert any exception to a Failure
  static Failure handleException(Object exception, [StackTrace? stackTrace]) {
    // Log the error for debugging
    _logError(exception, stackTrace);

    // Convert exception to appropriate Failure
    if (exception is AppException) {
      return _handleAppException(exception);
    } else if (exception is DriftWrappedException) {
      return Failure.database(exception.message);
    } else if (exception is AuthException) {
      return _handleAuthException(exception);
    } else if (exception is PostgrestException) {
      return _handlePostgrestException(exception);
    } else if (exception is SocketException) {
      return Failure.network('Network connection failed: ${exception.message}');
    } else if (exception is HttpException) {
      return Failure.network('HTTP error: ${exception.message}');
    } else if (exception is FormatException) {
      return Failure.parsing('Data format error: ${exception.message}');
    } else if (exception is TimeoutException) {
      return Failure.timeout('Operation timed out: ${exception.message}');
    } else if (exception is FileSystemException) {
      return Failure.storage('File system error: ${exception.message}');
    } else {
      return Failure.unknown('Unexpected error: ${exception.toString()}');
    }
  }

  /// Handle app-specific exceptions
  static Failure _handleAppException(AppException exception) {
    switch (exception.runtimeType) {
      case DatabaseException _:
        return Failure.database(exception.message);
      case NetworkException _:
        return Failure.network(exception.message);
      case ValidationException _:
        return Failure.validation(exception.message);
      case NotFoundException _:
        return Failure.notFound(exception.message);
      case BusinessLogicException _:
        return Failure.businessLogic(exception.message);
      case AuthenticationException _:
        return Failure.authentication(exception.message);
      case AuthorizationException _:
        return Failure.authorization(exception.message);
      case SyncException _:
        return Failure.sync(exception.message);
      case AppStorageException _:
        return Failure.storage(exception.message);
      case ParsingException _:
        return Failure.parsing(exception.message);
      case TimeoutException _:
        return Failure.timeout(exception.message);
      default:
        return Failure.unknown(exception.message);
    }
  }

  /// Handle Supabase auth exceptions
  static Failure _handleAuthException(AuthException exception) {
    switch (exception.statusCode) {
      case '401':
        return const Failure.authentication('Invalid credentials');
      case '403':
        return const Failure.authorization('Access denied');
      case '422':
        return Failure.validation(exception.message);
      case '429':
        return const Failure.network(
          'Too many requests. Please try again later.',
        );
      default:
        return Failure.authentication(exception.message);
    }
  }

  /// Handle Supabase Postgrest exceptions
  static Failure _handlePostgrestException(PostgrestException exception) {
    switch (exception.code) {
      case '23505': // Unique violation
        return const Failure.validation('Duplicate entry found');
      case '23503': // Foreign key violation
        return const Failure.validation('Referenced data not found');
      case '23502': // Not null violation
        return const Failure.validation('Required field is missing');
      case '42P01': // Undefined table
        return const Failure.database('Database table not found');
      default:
        return Failure.database(exception.message);
    }
  }

  /// Log error for debugging purposes
  static void _logError(Object exception, [StackTrace? stackTrace]) {
    if (kDebugMode) {
      debugPrint('Error: $exception');
      if (stackTrace != null) {
        debugPrint('Stack trace: $stackTrace');
      }
    }
  }

  /// Handle async operations with error conversion
  static Future<T> handleAsync<T>(Future<T> Function() operation) async {
    try {
      return await operation();
    } catch (e, stackTrace) {
      throw handleException(e, stackTrace);
    }
  }

  /// Handle sync operations with error conversion
  static T handleSync<T>(T Function() operation) {
    try {
      return operation();
    } catch (e, stackTrace) {
      throw handleException(e, stackTrace);
    }
  }
}
