import 'package:freezed_annotation/freezed_annotation.dart';

import 'failures.dart';

part 'result.freezed.dart';

/// Result type for handling success and failure states.
/// Provides a functional approach to error handling without exceptions.
@freezed
class Result<T> with _$Result<T> {
  const factory Result.success(T data) = Success<T>;
  const factory Result.failure(Failure failure) = FailureResult<T>;
}

/// Extension methods for Result type
extension ResultExtension<T> on Result<T> {
  /// Check if the result is successful
  bool get isSuccess => when(success: (_) => true, failure: (_) => false);

  /// Check if the result is a failure
  bool get isFailure => !isSuccess;

  /// Get the data if successful, otherwise return null
  T? get dataOrNull => when(success: (data) => data, failure: (_) => null);

  /// Get the failure if failed, otherwise return null
  Failure? get failureOrNull =>
      when(success: (_) => null, failure: (failure) => failure);

  /// Transform the data if successful
  Result<R> map<R>(R Function(T data) transform) => when(
    success: (data) => Result.success(transform(data)),
    failure: Result.failure,
  );

  /// Transform the data asynchronously if successful
  Future<Result<R>> mapAsync<R>(Future<R> Function(T data) transform) async => when(
      success: (data) async {
        try {
          final result = await transform(data);
          return Result.success(result);
        } on Exception catch (e) {
          return Result.failure(Failure.unknown(e.toString()));
        }
      },
      failure: (failure) => Future.value(Result.failure(failure)),
    );

  /// Chain another operation if successful
  Result<R> flatMap<R>(Result<R> Function(T data) operation) =>
      when(success: operation, failure: Result.failure);

  /// Chain another async operation if successful
  Future<Result<R>> flatMapAsync<R>(
    Future<Result<R>> Function(T data) operation,
  ) async => when(
    success: (data) => operation(data),
    failure: (failure) => Future.value(Result.failure(failure)),
  );

  /// Execute a side effect if successful
  Result<T> onSuccess(void Function(T data) action) {
    when(success: (data) => action(data), failure: (_) {});
    return this;
  }

  /// Execute a side effect if failed
  Result<T> onFailure(void Function(Failure failure) action) {
    when(success: (_) {}, failure: (failure) => action(failure));
    return this;
  }

  /// Get the data or throw the failure
  T getOrThrow() =>
      when(success: (data) => data, failure: (failure) => throw failure);

  /// Get the data or return a default value
  T getOrElse(T defaultValue) =>
      when(success: (data) => data, failure: (_) => defaultValue);

  /// Get the data or compute a default value
  T getOrElseCompute(T Function() defaultValue) =>
      when(success: (data) => data, failure: (_) => defaultValue());
}

/// Utility functions for creating Results
class ResultUtils {
  ResultUtils._();

  /// Create a successful result
  static Result<T> success<T>(T data) => Result.success(data);

  /// Create a failed result
  static Result<T> failure<T>(Failure failure) => Result.failure(failure);

  /// Execute an operation and wrap it in a Result
  static Result<T> trySync<T>(T Function() operation) {
    try {
      return Result.success(operation());
    } on Exception catch (e) {
      return Result.failure(Failure.unknown(e.toString()));
    }
  }

  /// Execute an async operation and wrap it in a Result
  static Future<Result<T>> tryAsync<T>(Future<T> Function() operation) async {
    try {
      final result = await operation();
      return Result.success(result);
    } on Exception catch (e) {
      return Result.failure(Failure.unknown(e.toString()));
    }
  }

  /// Combine multiple Results into one
  static Result<List<T>> combine<T>(List<Result<T>> results) {
    final data = <T>[];
    for (final result in results) {
      if (result.isFailure) {
        return Result.failure(result.failureOrNull!);
      }
      data.add(result.dataOrNull as T);
    }
    return Result.success(data);
  }
}
