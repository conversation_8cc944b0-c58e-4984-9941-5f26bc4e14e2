import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../config/app_config.dart';
import '../datasources/database.dart';
import '../providers/theme_provider.dart';
import '../theme/app_theme.dart';
import 'provider_overrides.dart';

// Missing initialization providers - temporary implementations for testing
final appConfigInitializedProvider = FutureProvider<bool>((ref) async => true);

final databaseInitializedProvider = FutureProvider<bool>((ref) async => true);

final dependenciesInitializedProvider = FutureProvider<bool>(
  (ref) async => true,
);

/// Test helpers for dependency injection and widget testing.
/// Provides utilities for creating test environments with proper DI setup.
class TestHelpers {
  TestHelpers._();

  /// Create a test widget with provider scope and overrides
  static Widget createTestWidget({
    required Widget child,
    List<Override>? overrides,
    AppDatabase? mockDatabase,
    AppConfig? mockAppConfig,
  }) {
    final testOverrides = ProviderOverrides.createWidgetTestOverrides(
      mockDatabase: mockDatabase,
      mockAppConfig: mockAppConfig,
    );

    final allOverrides = [
      ...testOverrides,
      if (overrides != null) ...overrides,
    ];

    return ProviderScope(
      overrides: allOverrides,
      child: MaterialApp(theme: AppTheme.lightTheme, home: child),
    );
  }

  /// Create a test app with full provider setup
  static Widget createTestApp({
    Widget? home,
    List<Override>? overrides,
    AppDatabase? mockDatabase,
    AppConfig? mockAppConfig,
    ThemeMode? themeMode,
  }) {
    final testOverrides = ProviderOverrides.createWidgetTestOverrides(
      mockDatabase: mockDatabase,
      mockAppConfig: mockAppConfig,
    );

    final themeOverrides = ProviderOverrides.createThemeOverrides(
      themeMode: themeMode ?? ThemeMode.light,
    );

    final allOverrides = [
      ...testOverrides,
      ...themeOverrides,
      if (overrides != null) ...overrides,
    ];

    return ProviderScope(
      overrides: allOverrides,
      child: Consumer(
        builder: (context, ref, child) {
          final lightTheme = ref.watch(lightThemeProvider);
          final darkTheme = ref.watch(darkThemeProvider);
          final currentThemeMode = ref.watch(themeModeProvider);

          return MaterialApp(
            theme: lightTheme,
            darkTheme: darkTheme,
            themeMode: currentThemeMode,
            home: home ?? const Scaffold(body: Center(child: Text('Test App'))),
          );
        },
      ),
    );
  }

  /// Create a provider container for testing
  static ProviderContainer createTestContainer({
    List<Override>? overrides,
    AppDatabase? mockDatabase,
    AppConfig? mockAppConfig,
  }) {
    final testOverrides = ProviderOverrides.createTestOverrides(
      mockDatabase: mockDatabase,
      mockAppConfig: mockAppConfig,
    );

    final allOverrides = [
      ...testOverrides,
      if (overrides != null) ...overrides,
    ];

    return ProviderContainer(overrides: allOverrides);
  }

  /// Pump a widget with provider scope in widget tests
  static Future<void> pumpWidgetWithProviders(
    Object tester, {
    required Widget widget,
    List<Override>? overrides,
    AppDatabase? mockDatabase,
    AppConfig? mockAppConfig,
  }) async {
    await (tester as dynamic).pumpWidget(
      createTestWidget(
        child: widget,
        overrides: overrides,
        mockDatabase: mockDatabase,
        mockAppConfig: mockAppConfig,
      ),
    );
  }

  /// Pump an app with full provider setup in widget tests
  static Future<void> pumpAppWithProviders(
    Object tester, {
    Widget? home,
    List<Override>? overrides,
    AppDatabase? mockDatabase,
    AppConfig? mockAppConfig,
    ThemeMode? themeMode,
  }) async {
    await (tester as dynamic).pumpWidget(
      createTestApp(
        home: home,
        overrides: overrides,
        mockDatabase: mockDatabase,
        mockAppConfig: mockAppConfig,
        themeMode: themeMode,
      ),
    );
  }

  /// Create a mock app config for testing
  static MockAppConfig createMockAppConfig({
    String? appName,
    String? appVersion,
    String? databaseName,
    bool? debugMode,
    bool? enableLogging,
    bool? isSupabaseConfigured,
  }) {
    final mockConfig = MockAppConfig();

    // Set up mock behavior
    if (appName != null) {
      mockConfig.setAppName(appName);
    }
    if (appVersion != null) {
      mockConfig.setAppVersion(appVersion);
    }
    if (databaseName != null) {
      mockConfig.setDatabaseName(databaseName);
    }
    if (debugMode != null) {
      mockConfig.setDebugMode(debug: debugMode);
    }
    if (enableLogging != null) {
      mockConfig.setEnableLogging(logging: enableLogging);
    }
    if (isSupabaseConfigured != null) {
      mockConfig.setSupabaseConfigured(configured: isSupabaseConfigured);
    }

    return mockConfig;
  }

  /// Wait for providers to initialize in tests
  static Future<void> waitForProviderInitialization(
    ProviderContainer container,
  ) async {
    // Wait for app config initialization
    try {
      await container.read(appConfigInitializedProvider.future);
    } on Exception {
      // Ignore initialization errors in tests
    }

    // Wait for database initialization
    try {
      await container.read(databaseInitializedProvider.future);
    } on Exception {
      // Ignore initialization errors in tests
    }

    // Wait for dependencies initialization
    try {
      await container.read(dependenciesInitializedProvider.future);
    } on Exception {
      // Ignore initialization errors in tests
    }
  }
}

/// Mock implementation of AppConfig for testing
class MockAppConfig {
  String _appName = 'Test App';
  String _appVersion = '1.0.0-test';
  String _databaseName = 'test.db';
  bool _debugMode = true;
  bool _enableLogging = false;
  bool _isSupabaseConfigured = false;

  void setAppName(String name) => _appName = name;
  void setAppVersion(String version) => _appVersion = version;
  void setDatabaseName(String name) => _databaseName = name;
  void setDebugMode({required bool debug}) {
    _debugMode = debug;
  }

  void setEnableLogging({required bool logging}) {
    _enableLogging = logging;
  }

  void setSupabaseConfigured({required bool configured}) {
    _isSupabaseConfigured = configured;
  }

  String get appName => _appName;
  String get appVersion => _appVersion;
  String get databaseName => _databaseName;
  bool get debugMode => _debugMode;
  bool get enableLogging => _enableLogging;
  bool get isSupabaseConfigured => _isSupabaseConfigured;
  String get supabaseUrl => 'https://test.supabase.co';
  String get supabaseAnonKey => 'test_anon_key';
  String? get supabaseServiceRoleKey => 'test_service_role_key';

  Map<String, dynamic> get configSummary => {
    'appName': appName,
    'appVersion': appVersion,
    'databaseName': databaseName,
    'debugMode': debugMode,
    'enableLogging': enableLogging,
    'isSupabaseConfigured': isSupabaseConfigured,
  };
}
