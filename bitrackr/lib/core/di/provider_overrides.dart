import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../config/app_config.dart';
import '../datasources/database.dart';
import '../providers/app_config_provider.dart';
import '../providers/database_provider.dart';
import '../providers/theme_provider.dart';

/// Provider overrides for testing and development.
/// Provides utilities for mocking dependencies in tests.
class ProviderOverrides {
  ProviderOverrides._();

  /// Create overrides for testing with mock dependencies
  static List<Override> createTestOverrides({
    AppDatabase? mockDatabase,
    AppConfig? mockAppConfig,
    String? mockDatabasePath,
  }) {
    final overrides = <Override>[];

    // Override database provider if mock is provided
    if (mockDatabase != null) {
      overrides.add(databaseProvider.overrideWithValue(mockDatabase));
    }

    // Override app config provider if mock is provided
    if (mockAppConfig != null) {
      overrides.add(appConfigProvider.overrideWithValue(mockAppConfig));
    }

    // Override database path provider if mock path is provided
    if (mockDatabasePath != null) {
      overrides.add(databasePathProvider.overrideWithValue(mockDatabasePath));
    }

    return overrides;
  }

  /// Create overrides for development environment
  static List<Override> createDevelopmentOverrides({
    bool? forceDebugMode,
    bool? forceLogging,
  }) {
    final overrides = <Override>[];

    // Override debug mode if specified
    if (forceDebugMode != null) {
      overrides.add(debugModeProvider.overrideWithValue(forceDebugMode));
    }

    // Override logging if specified
    if (forceLogging != null) {
      overrides.add(loggingEnabledProvider.overrideWithValue(forceLogging));
    }

    return overrides;
  }

  /// Create overrides for specific theme mode
  static List<Override> createThemeOverrides({ThemeMode? themeMode}) {
    final overrides = <Override>[];

    if (themeMode != null) {
      overrides.add(
        themeModeProvider.overrideWith(
          (ref) => ThemeModeNotifier.test(themeMode),
        ),
      );
    }

    return overrides;
  }

  /// Create overrides for integration testing
  static List<Override> createIntegrationTestOverrides({
    String? testDatabasePath,
    bool? disableAnimations,
  }) {
    final overrides = <Override>[];

    // Use in-memory database for integration tests
    if (testDatabasePath != null) {
      overrides.add(databasePathProvider.overrideWithValue(testDatabasePath));
    }

    // Force debug mode for integration tests
    overrides
      ..add(debugModeProvider.overrideWithValue(true))
      ..add(loggingEnabledProvider.overrideWithValue(true));

    return overrides;
  }

  /// Create overrides for widget testing
  static List<Override> createWidgetTestOverrides({
    AppDatabase? mockDatabase,
    AppConfig? mockAppConfig,
  }) {
    final overrides = <Override>[];

    // Use mock database for widget tests
    if (mockDatabase != null) {
      overrides.add(databaseProvider.overrideWithValue(mockDatabase));
    }

    // Use mock app config for widget tests
    if (mockAppConfig != null) {
      overrides.add(appConfigProvider.overrideWithValue(mockAppConfig));
    }

    // Force light theme for consistent widget tests
    overrides.add(
      themeModeProvider.overrideWith(
        (ref) => ThemeModeNotifier.test(ThemeMode.light),
      ),
    );

    return overrides;
  }

  /// Create overrides for performance testing
  static List<Override> createPerformanceTestOverrides() => <Override>[
    // Disable logging for performance tests
    loggingEnabledProvider.overrideWithValue(false),
    // Use system theme for performance tests
    themeModeProvider.overrideWith(
      (ref) => ThemeModeNotifier.test(ThemeMode.system),
    ),
  ];

  /// Combine multiple override lists
  static List<Override> combineOverrides(List<List<Override>> overrideLists) {
    final combinedOverrides = <Override>[];
    for (final overrides in overrideLists) {
      combinedOverrides.addAll(overrides);
    }
    return combinedOverrides;
  }
}
