import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../config/app_config.dart';
import '../datasources/database.dart';
import '../providers/app_config_provider.dart';
import '../providers/database_provider.dart';
import 'service_locator.dart';

/// Dependency injection container for the bitrackr application.
/// Provides centralized dependency management using Riverpod.
class InjectionContainer {
  InjectionContainer._();

  static InjectionContainer? _instance;
  // ignore: prefer_constructors_over_static_methods
  static InjectionContainer get instance =>
      _instance ??= InjectionContainer._();

  /// Initialize the dependency injection container
  Future<void> initialize() async {
    // Initialize app configuration
    await AppConfig.instance.initialize();

    // Setup core services in service locator
    ServiceSetup.setupCoreServices();
  }

  /// Get provider container with overrides for testing
  ProviderContainer getProviderContainer({
    List<Override> overrides = const [],
  }) => ProviderContainer(overrides: overrides);

  /// Get provider container for testing with mock dependencies
  ProviderContainer getTestProviderContainer({
    AppDatabase? mockDatabase,
    AppConfig? mockConfig,
    List<Override> additionalOverrides = const [],
  }) {
    final overrides = <Override>[...additionalOverrides];

    if (mockDatabase != null) {
      overrides.add(databaseProvider.overrideWithValue(mockDatabase));
    }

    if (mockConfig != null) {
      overrides.add(appConfigProvider.overrideWithValue(mockConfig));
    }

    return ProviderContainer(overrides: overrides);
  }

  /// Dispose all resources
  void dispose() {
    // Clean up any resources if needed
  }
}

/// Provider for the injection container
final injectionContainerProvider = Provider<InjectionContainer>(
  (ref) => InjectionContainer.instance,
);

/// Provider for checking if all dependencies are initialized
final dependenciesInitializedProvider = FutureProvider<bool>((ref) async {
  try {
    // Check if app config is initialized
    final configInitialized = await ref.watch(
      appConfigInitializedProvider.future,
    );

    // Check if database is initialized
    final databaseInitialized = await ref.watch(
      databaseInitializedProvider.future,
    );

    return configInitialized && databaseInitialized;
  } on Exception {
    return false;
  }
});

/// Provider for dependency health check
final dependencyHealthProvider = FutureProvider<Map<String, dynamic>>((
  ref,
) async {
  try {
    final configInitialized = await ref.watch(
      appConfigInitializedProvider.future,
    );
    final databaseHealth = await ref.watch(databaseHealthProvider.future);
    final supabaseConfigured = ref.watch(supabaseConfigStatusProvider);

    return {
      'overall_healthy': configInitialized && databaseHealth['healthy'] == true,
      'config_initialized': configInitialized,
      'database_healthy': databaseHealth['healthy'],
      'supabase_configured': supabaseConfigured,
      'database_details': databaseHealth,
      'timestamp': DateTime.now().toIso8601String(),
    };
  } on Exception catch (e) {
    return {
      'overall_healthy': false,
      'error': e.toString(),
      'timestamp': DateTime.now().toIso8601String(),
    };
  }
});
